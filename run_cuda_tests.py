#!/usr/bin/env python3
"""
CUDA Test Runner

Comprehensive test runner for CUDA 12.9 verification.
Provides different test modes and detailed reporting.
"""

import sys
import argparse
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(cmd: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
    """Run a command and return the result."""
    logger.info(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=capture_output,
            text=True,
            check=False
        )
        return result
    except Exception as e:
        logger.error(f"Error running command: {e}")
        raise


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    logger.info("Checking dependencies...")
    
    required_packages = [
        'pytest',
        'numpy',
    ]
    
    optional_packages = [
        'cupy',
        'torch',
        'pycuda',
        'pynvml'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} is available")
        except ImportError:
            missing_required.append(package)
            logger.error(f"✗ {package} is missing (required)")
    
    for package in optional_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} is available")
        except ImportError:
            missing_optional.append(package)
            logger.warning(f"⚠ {package} is missing (optional)")
    
    if missing_required:
        logger.error(f"Missing required packages: {missing_required}")
        logger.error("Install with: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        logger.warning(f"Missing optional packages: {missing_optional}")
        logger.warning("Some tests may be skipped. Install with: pip install " + " ".join(missing_optional))
    
    return True


def run_basic_tests() -> bool:
    """Run basic CUDA functionality tests."""
    logger.info("Running basic CUDA tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "test_cuda_verification.py",
        "-v",
        "-m", "basic",
        "--tb=short"
    ]
    
    result = run_command(cmd, capture_output=False)
    return result.returncode == 0


def run_computation_tests() -> bool:
    """Run CUDA computation tests."""
    logger.info("Running CUDA computation tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "test_cuda_verification.py",
        "-v",
        "-m", "computation",
        "--tb=short"
    ]
    
    result = run_command(cmd, capture_output=False)
    return result.returncode == 0


def run_performance_tests() -> bool:
    """Run CUDA performance tests."""
    logger.info("Running CUDA performance tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "test_cuda_performance.py",
        "-v",
        "-m", "performance",
        "--tb=short"
    ]
    
    result = run_command(cmd, capture_output=False)
    return result.returncode == 0


def run_all_tests() -> bool:
    """Run all CUDA tests."""
    logger.info("Running all CUDA tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "test_cuda_verification.py",
        "test_cuda_performance.py",
        "-v",
        "--tb=short"
    ]
    
    result = run_command(cmd, capture_output=False)
    return result.returncode == 0


def run_quick_tests() -> bool:
    """Run quick CUDA tests (excluding slow performance tests)."""
    logger.info("Running quick CUDA tests...")
    
    cmd = [
        sys.executable, "-m", "pytest",
        "test_cuda_verification.py",
        "test_cuda_performance.py",
        "-v",
        "-m", "not slow",
        "--tb=short"
    ]
    
    result = run_command(cmd, capture_output=False)
    return result.returncode == 0


def generate_system_info():
    """Generate system information report."""
    logger.info("Generating system information...")
    
    try:
        from cuda_utils import check_cuda_availability, get_gpu_properties, get_memory_info
        
        print("\n" + "="*60)
        print("CUDA SYSTEM INFORMATION")
        print("="*60)
        
        # CUDA availability
        availability = check_cuda_availability()
        print("\nCUDA Library Availability:")
        for lib, available in availability.items():
            if lib != 'errors':
                status = "✓" if available else "✗"
                print(f"  {status} {lib}: {available}")
        
        if availability['errors']:
            print("\nErrors:")
            for lib, error in availability['errors'].items():
                print(f"  {lib}: {error}")
        
        # GPU properties
        gpu_props = get_gpu_properties()
        if gpu_props:
            print(f"\nGPU Devices ({len(gpu_props)} found):")
            for i, gpu in enumerate(gpu_props):
                print(f"  GPU {i}: {gpu['name']}")
                print(f"    Compute Capability: {gpu['compute_capability']}")
                print(f"    Memory: {gpu['total_memory_gb']:.2f} GB")
                if 'multiprocessor_count' in gpu:
                    print(f"    Multiprocessors: {gpu['multiprocessor_count']}")
        
        # Memory info
        memory_info = get_memory_info()
        if memory_info:
            print("\nGPU Memory Status:")
            for gpu_id, info in memory_info.items():
                print(f"  {gpu_id}:")
                print(f"    Total: {info['total_gb']:.2f} GB")
                print(f"    Allocated: {info['allocated_gb']:.2f} GB")
                print(f"    Free: {info['free_gb']:.2f} GB")
                print(f"    Utilization: {info['utilization_percent']:.1f}%")
        
        print("\n" + "="*60)
        
    except Exception as e:
        logger.error(f"Error generating system info: {e}")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="CUDA 12.9 Verification Test Suite",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Test Modes:
  basic       - Basic CUDA availability and version tests
  computation - CUDA computation verification tests  
  performance - CUDA performance benchmark tests
  quick       - All tests except slow performance tests
  all         - All tests including slow performance tests
  info        - Display system information only

Examples:
  python run_cuda_tests.py basic
  python run_cuda_tests.py all
  python run_cuda_tests.py info
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['basic', 'computation', 'performance', 'quick', 'all', 'info'],
        help='Test mode to run'
    )
    
    parser.add_argument(
        '--no-deps-check',
        action='store_true',
        help='Skip dependency checking'
    )
    
    args = parser.parse_args()
    
    # Generate system info
    if args.mode == 'info':
        generate_system_info()
        return 0
    
    # Check dependencies
    if not args.no_deps_check:
        if not check_dependencies():
            logger.error("Dependency check failed. Use --no-deps-check to skip.")
            return 1
    
    # Run tests based on mode
    success = False
    
    if args.mode == 'basic':
        success = run_basic_tests()
    elif args.mode == 'computation':
        success = run_computation_tests()
    elif args.mode == 'performance':
        success = run_performance_tests()
    elif args.mode == 'quick':
        success = run_quick_tests()
    elif args.mode == 'all':
        success = run_all_tests()
    
    if success:
        logger.info("All tests passed! ✓")
        return 0
    else:
        logger.error("Some tests failed! ✗")
        return 1


if __name__ == "__main__":
    sys.exit(main())
