#!/usr/bin/env python3
"""
CUDA Test Suite Demo

Quick demonstration of the CUDA verification capabilities.
"""

import sys
import time
from pathlib import Path

def print_header(title: str):
    """Print a formatted header."""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n--- {title} ---")

def main():
    """Run the demo."""
    print_header("CUDA 12.9 Verification Test Suite Demo")
    
    print("""
This demo shows how to use the comprehensive CUDA test suite
to verify that CUDA 12.9 is properly installed and functioning.
    """)
    
    print_section("Available Test Files")
    
    test_files = [
        ("test_cuda_verification.py", "Main CUDA functionality tests"),
        ("test_cuda_performance.py", "Performance benchmarking tests"),
        ("cuda_utils.py", "Shared CUDA utility functions"),
        ("run_cuda_tests.py", "Test runner with multiple modes"),
        ("test_config.py", "Configuration parameters")
    ]
    
    for filename, description in test_files:
        status = "✓" if Path(filename).exists() else "✗"
        print(f"  {status} {filename:<25} - {description}")
    
    print_section("Quick System Check")
    
    try:
        from cuda_utils import check_cuda_availability, get_gpu_properties
        
        # Check CUDA availability
        availability = check_cuda_availability()
        print("\nCUDA Library Status:")
        
        for lib in ['cupy', 'torch', 'pycuda']:
            if lib in availability:
                status = "✓ Available" if availability[lib] else "✗ Not available"
                print(f"  {lib:<10}: {status}")
                if availability[lib] and f'{lib}_version' in availability:
                    print(f"             Version: {availability[f'{lib}_version']}")
        
        # Check GPU devices
        gpu_props = get_gpu_properties()
        if gpu_props:
            print(f"\nGPU Devices Found: {len(gpu_props)}")
            for i, gpu in enumerate(gpu_props):
                print(f"  GPU {i}: {gpu['name']}")
                print(f"         Memory: {gpu['total_memory_gb']:.1f} GB")
                print(f"         Compute: {gpu['compute_capability']}")
        else:
            print("\nNo GPU devices detected")
            
    except Exception as e:
        print(f"\nError during system check: {e}")
        print("This is normal if CUDA libraries are not yet installed.")
    
    print_section("Usage Examples")
    
    examples = [
        ("Basic verification", "python run_cuda_tests.py basic"),
        ("System information", "python run_cuda_tests.py info"),
        ("Quick tests", "python run_cuda_tests.py quick"),
        ("Full test suite", "python run_cuda_tests.py all"),
        ("Performance only", "python run_cuda_tests.py performance"),
        ("Using Make", "make test-basic"),
        ("Direct pytest", "pytest test_cuda_verification.py -m basic -v")
    ]
    
    for description, command in examples:
        print(f"\n  {description}:")
        print(f"    {command}")
    
    print_section("Installation Steps")
    
    steps = [
        "1. Install dependencies: pip install -r requirements.txt",
        "2. Run basic tests: python run_cuda_tests.py basic",
        "3. Check system info: python run_cuda_tests.py info",
        "4. Run full suite: python run_cuda_tests.py all"
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print_section("Test Categories")
    
    categories = [
        ("Basic Tests", "CUDA availability, version verification, device detection"),
        ("Computation Tests", "Vector addition, matrix multiplication, result verification"),
        ("Performance Tests", "Memory transfer speeds, computation throughput"),
        ("Memory Tests", "Allocation, deallocation, memory management"),
        ("Benchmark Tests", "Detailed performance metrics and comparisons")
    ]
    
    for category, description in categories:
        print(f"\n  {category}:")
        print(f"    {description}")
    
    print_section("Expected Results")
    
    print("""
Successful basic test output:
  ✓ CUDA libraries available
  ✓ GPU devices detected  
  ✓ CUDA version 12.9 verified
  ✓ Compute capability sufficient
  ✓ Memory allocation working
  ✓ Basic computations correct

Performance test output:
  Matrix multiplication: GPU 45ms vs CPU 1250ms (27x speedup)
  Vector operations: 4000 MOPS throughput
  Memory transfer: 11.8 GB/s bandwidth
    """)
    
    print_section("Next Steps")
    
    print("""
1. Install CUDA dependencies:
   pip install -r requirements.txt

2. Run the basic verification:
   python run_cuda_tests.py basic

3. If successful, run the full suite:
   python run_cuda_tests.py all

4. Check the README.md for detailed documentation
    """)
    
    print_header("Demo Complete")
    print("\nThe CUDA test suite is ready to use!")
    print("Run 'python run_cuda_tests.py basic' to get started.")

if __name__ == "__main__":
    main()
