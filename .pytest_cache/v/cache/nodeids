["test_cuda_verification.py::TestCUDAAvailability::test_cuda_libraries_available", "test_cuda_verification.py::TestCUDAAvailability::test_gpu_devices_detected", "test_cuda_verification.py::TestCUDAVersion::test_compute_capability_sufficient", "test_cuda_verification.py::TestCUDAVersion::test_cuda_version_12_9", "test_cuda_verification.py::TestGPUProperties::test_gpu_memory_sufficient", "test_cuda_verification.py::TestGPUProperties::test_gpu_multiprocessors"]