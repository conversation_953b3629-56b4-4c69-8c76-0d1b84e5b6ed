# CUDA 12.9 Verification Test Suite

A comprehensive Python test suite to verify that CUDA 12.9 is properly installed and functioning correctly across multiple libraries and use cases.

## Overview

This test suite provides thorough verification of CUDA 12.9 installation including:

- ✅ CUDA library availability (CuPy, PyTorch, PyCUDA)
- ✅ CUDA runtime version verification
- ✅ GPU device detection and properties
- ✅ Basic CUDA computations (vector addition, matrix multiplication)
- ✅ Performance benchmarking (memory transfer, computation throughput)
- ✅ Memory management verification

## Quick Start

### 1. Install Dependencies

```bash
# Install required dependencies
pip install -r requirements.txt

# Or install manually:
pip install pytest numpy cupy-cuda12x torch pycuda
```

### 2. Run Tests

```bash
# Quick verification (recommended first run)
python run_cuda_tests.py basic

# All tests including performance benchmarks
python run_cuda_tests.py all

# System information only
python run_cuda_tests.py info
```

## Test Modes

| Mode | Description | Duration |
|------|-------------|----------|
| `basic` | CUDA availability, version, and device detection | ~30 seconds |
| `computation` | Basic CUDA computations verification | ~1 minute |
| `performance` | Performance benchmarks and throughput tests | ~5 minutes |
| `quick` | All tests except slow performance tests | ~2 minutes |
| `all` | Complete test suite including all benchmarks | ~10 minutes |
| `info` | Display system information only | ~5 seconds |

## Test Structure

### Core Test Files

- **`test_cuda_verification.py`** - Main CUDA functionality tests
- **`test_cuda_performance.py`** - Performance benchmarking tests
- **`cuda_utils.py`** - Shared CUDA utility functions
- **`run_cuda_tests.py`** - Test runner with multiple modes

### Test Categories

#### 1. CUDA Availability Tests (`TestCUDAAvailability`)
- Verify CUDA libraries are importable and functional
- Check GPU device detection
- Validate basic CUDA runtime access

#### 2. Version Verification Tests (`TestCUDAVersion`)
- Confirm CUDA runtime version is 12.9
- Verify compute capability compatibility
- Check driver version compatibility

#### 3. GPU Properties Tests (`TestGPUProperties`)
- Validate sufficient GPU memory (≥1GB)
- Check multiprocessor availability
- Test memory allocation/deallocation

#### 4. Basic Computation Tests (`TestBasicComputation`)
- Vector addition using CuPy
- Matrix multiplication using PyTorch
- Result verification against CPU computations

#### 5. Performance Tests (`TestMemoryTransferPerformance`, `TestComputationPerformance`)
- Host-to-device memory transfer speeds
- Device-to-host memory transfer speeds
- Matrix multiplication performance vs CPU
- Vector operations throughput

## Requirements

### System Requirements
- CUDA 12.9 installed and configured
- Compatible GPU with compute capability ≥3.5
- Python 3.8+

### Python Dependencies
- **Required**: `pytest`, `numpy`
- **CUDA Libraries**: `cupy-cuda12x`, `torch`, `pycuda`
- **Utilities**: `psutil`, `GPUtil`, `py3nvml`

## Usage Examples

### Basic Verification
```bash
# Check if CUDA is working
python run_cuda_tests.py basic

# View system information
python run_cuda_tests.py info
```

### Advanced Testing
```bash
# Run computation tests only
python run_cuda_tests.py computation

# Full performance benchmarking
python run_cuda_tests.py performance

# Quick test (skip slow benchmarks)
python run_cuda_tests.py quick
```

### Using pytest Directly
```bash
# Run specific test categories
pytest test_cuda_verification.py -m basic -v
pytest test_cuda_verification.py -m computation -v
pytest test_cuda_performance.py -m performance -v

# Run tests with specific markers
pytest -m "cuda and not slow" -v
pytest -m "performance" -v
```

## Expected Output

### Successful Basic Test
```
CUDA SYSTEM INFORMATION
========================================
CUDA Library Availability:
  ✓ cupy: True
  ✓ torch: True
  ✓ pycuda: True

GPU Devices (1 found):
  GPU 0: NVIDIA GeForce RTX 4090
    Compute Capability: 8.9
    Memory: 24.00 GB
    Multiprocessors: 128

test_cuda_verification.py::TestCUDAAvailability::test_cuda_libraries_available PASSED
test_cuda_verification.py::TestCUDAAvailability::test_gpu_devices_detected PASSED
test_cuda_verification.py::TestCUDAVersion::test_cuda_version_12_9 PASSED
```

### Performance Test Results
```
MatMul 2048x2048: GPU 45.2ms (379.2 GFLOPS), CPU 1250.3ms (13.7 GFLOPS), Speedup: 27.7x
Vector Add 50.0M: GPU 12.5ms (4000 MOPS), CPU 89.2ms (561 MOPS), Speedup: 7.1x
H2D Transfer 500MB: 42.3ms, Bandwidth: 11.8 GB/s
```

## Troubleshooting

### Common Issues

1. **CUDA Not Found**
   ```
   Error: No CUDA libraries are available
   ```
   - Verify CUDA 12.9 is installed: `nvcc --version`
   - Check GPU driver compatibility
   - Reinstall CUDA libraries: `pip install cupy-cuda12x torch`

2. **Version Mismatch**
   ```
   Error: CUDA version mismatch. Expected: 12.9, Found: {'torch': '11.8'}
   ```
   - Install correct PyTorch version: `pip install torch --index-url https://download.pytorch.org/whl/cu129`
   - Verify CuPy version: `pip install cupy-cuda12x`

3. **Memory Errors**
   ```
   Error: GPU has insufficient memory
   ```
   - Close other GPU applications
   - Reduce test data sizes in configuration
   - Check GPU memory usage: `nvidia-smi`

### Debug Mode
```bash
# Run with verbose output
python run_cuda_tests.py basic --no-deps-check

# Run specific failing test
pytest test_cuda_verification.py::TestCUDAVersion::test_cuda_version_12_9 -v -s
```

## Configuration

### Customizing Tests

Edit test parameters in the test files:
- Matrix sizes in performance tests
- Memory allocation sizes
- Performance thresholds
- Timeout values

### Environment Variables
```bash
export CUDA_VISIBLE_DEVICES=0  # Use specific GPU
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # Memory management
```

## Contributing

To add new tests:
1. Add test methods to appropriate test classes
2. Use proper pytest markers (`@pytest.mark.cuda`, `@pytest.mark.slow`)
3. Include error handling for missing libraries
4. Add documentation and expected results

## License

This test suite is provided as-is for CUDA verification purposes.
