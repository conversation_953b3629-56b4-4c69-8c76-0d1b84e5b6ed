"""
CUDA Performance Benchmarking Tests

This module contains performance tests to measure CUDA computation
and memory transfer speeds.
"""

import pytest
import numpy as np
import time
import warnings
from typing import Dict, Any, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)


class TestMemoryTransferPerformance:
    """Test GPU memory transfer performance."""
    
    @pytest.mark.cuda
    @pytest.mark.performance
    @pytest.mark.slow
    def test_host_to_device_transfer_speed(self):
        """Test host-to-device memory transfer speed."""
        try:
            import torch
            
            if not torch.cuda.is_available():
                pytest.skip("PyTorch CUDA not available")
            
            device = torch.device('cuda:0')
            
            # Test different data sizes
            sizes_mb = [10, 50, 100, 500]  # MB
            results = {}
            
            for size_mb in sizes_mb:
                elements = (size_mb * 1024 * 1024) // 4  # 4 bytes per float32
                data_cpu = torch.randn(elements, dtype=torch.float32)
                
                # Warm up
                _ = data_cpu.to(device)
                torch.cuda.synchronize()
                
                # Measure transfer time
                start_time = time.perf_counter()
                data_gpu = data_cpu.to(device)
                torch.cuda.synchronize()
                end_time = time.perf_counter()
                
                transfer_time = end_time - start_time
                bandwidth_gbps = (size_mb / 1024) / transfer_time  # GB/s
                
                results[size_mb] = {
                    'transfer_time_ms': transfer_time * 1000,
                    'bandwidth_gbps': bandwidth_gbps
                }
                
                logger.info(
                    f"H2D Transfer {size_mb}MB: {transfer_time*1000:.2f}ms, "
                    f"Bandwidth: {bandwidth_gbps:.2f} GB/s"
                )
                
                # Clean up
                del data_gpu
                torch.cuda.empty_cache()
            
            # Assert reasonable performance (at least 1 GB/s for larger transfers)
            large_transfer = max(results.keys())
            assert results[large_transfer]['bandwidth_gbps'] >= 1.0, (
                f"Host-to-device transfer too slow: "
                f"{results[large_transfer]['bandwidth_gbps']:.2f} GB/s"
            )
            
        except ImportError:
            pytest.skip("PyTorch not available for transfer speed test")
    
    @pytest.mark.cuda
    @pytest.mark.performance
    @pytest.mark.slow
    def test_device_to_host_transfer_speed(self):
        """Test device-to-host memory transfer speed."""
        try:
            import torch
            
            if not torch.cuda.is_available():
                pytest.skip("PyTorch CUDA not available")
            
            device = torch.device('cuda:0')
            
            # Test different data sizes
            sizes_mb = [10, 50, 100, 500]  # MB
            results = {}
            
            for size_mb in sizes_mb:
                elements = (size_mb * 1024 * 1024) // 4  # 4 bytes per float32
                data_gpu = torch.randn(elements, dtype=torch.float32, device=device)
                
                # Warm up
                _ = data_gpu.cpu()
                torch.cuda.synchronize()
                
                # Measure transfer time
                start_time = time.perf_counter()
                data_cpu = data_gpu.cpu()
                torch.cuda.synchronize()
                end_time = time.perf_counter()
                
                transfer_time = end_time - start_time
                bandwidth_gbps = (size_mb / 1024) / transfer_time  # GB/s
                
                results[size_mb] = {
                    'transfer_time_ms': transfer_time * 1000,
                    'bandwidth_gbps': bandwidth_gbps
                }
                
                logger.info(
                    f"D2H Transfer {size_mb}MB: {transfer_time*1000:.2f}ms, "
                    f"Bandwidth: {bandwidth_gbps:.2f} GB/s"
                )
                
                # Clean up
                del data_gpu
                torch.cuda.empty_cache()
            
            # Assert reasonable performance
            large_transfer = max(results.keys())
            assert results[large_transfer]['bandwidth_gbps'] >= 1.0, (
                f"Device-to-host transfer too slow: "
                f"{results[large_transfer]['bandwidth_gbps']:.2f} GB/s"
            )
            
        except ImportError:
            pytest.skip("PyTorch not available for transfer speed test")


class TestComputationPerformance:
    """Test GPU computation performance."""
    
    @pytest.mark.cuda
    @pytest.mark.performance
    @pytest.mark.slow
    def test_matrix_multiplication_performance(self):
        """Test matrix multiplication performance and compare with CPU."""
        try:
            import torch
            
            if not torch.cuda.is_available():
                pytest.skip("PyTorch CUDA not available")
            
            device = torch.device('cuda:0')
            
            # Test different matrix sizes
            sizes = [512, 1024, 2048]
            results = {}
            
            for size in sizes:
                # Create test matrices
                a_cpu = torch.randn(size, size, dtype=torch.float32)
                b_cpu = torch.randn(size, size, dtype=torch.float32)
                a_gpu = a_cpu.to(device)
                b_gpu = b_cpu.to(device)
                
                # Warm up GPU
                _ = torch.matmul(a_gpu, b_gpu)
                torch.cuda.synchronize()
                
                # Measure GPU performance
                start_time = time.perf_counter()
                for _ in range(5):  # Average over multiple runs
                    c_gpu = torch.matmul(a_gpu, b_gpu)
                torch.cuda.synchronize()
                gpu_time = (time.perf_counter() - start_time) / 5
                
                # Measure CPU performance (single run for large matrices)
                start_time = time.perf_counter()
                c_cpu = torch.matmul(a_cpu, b_cpu)
                cpu_time = time.perf_counter() - start_time
                
                # Calculate FLOPS (2 * n^3 operations for matrix multiplication)
                flops = 2 * size ** 3
                gpu_gflops = flops / (gpu_time * 1e9)
                cpu_gflops = flops / (cpu_time * 1e9)
                speedup = cpu_time / gpu_time
                
                results[size] = {
                    'gpu_time_ms': gpu_time * 1000,
                    'cpu_time_ms': cpu_time * 1000,
                    'gpu_gflops': gpu_gflops,
                    'cpu_gflops': cpu_gflops,
                    'speedup': speedup
                }
                
                logger.info(
                    f"MatMul {size}x{size}: GPU {gpu_time*1000:.2f}ms "
                    f"({gpu_gflops:.1f} GFLOPS), CPU {cpu_time*1000:.2f}ms "
                    f"({cpu_gflops:.1f} GFLOPS), Speedup: {speedup:.1f}x"
                )
                
                # Clean up
                del a_gpu, b_gpu, c_gpu
                torch.cuda.empty_cache()
            
            # Assert GPU provides reasonable speedup for larger matrices
            large_size = max(results.keys())
            assert results[large_size]['speedup'] >= 2.0, (
                f"GPU speedup too low for {large_size}x{large_size} matrix: "
                f"{results[large_size]['speedup']:.1f}x"
            )
            
        except ImportError:
            pytest.skip("PyTorch not available for performance test")
    
    @pytest.mark.cuda
    @pytest.mark.performance
    def test_vector_operations_performance(self):
        """Test vector operations performance."""
        try:
            import cupy as cp
            
            if not cp.cuda.is_available():
                pytest.skip("CuPy CUDA not available")
            
            # Test different vector sizes
            sizes = [1000000, 10000000, 50000000]  # 1M, 10M, 50M elements
            results = {}
            
            for size in sizes:
                # Create test vectors
                a_cpu = np.random.randn(size).astype(np.float32)
                b_cpu = np.random.randn(size).astype(np.float32)
                a_gpu = cp.asarray(a_cpu)
                b_gpu = cp.asarray(b_cpu)
                
                # Warm up
                _ = a_gpu + b_gpu
                cp.cuda.Stream.null.synchronize()
                
                # Measure GPU performance
                start_time = time.perf_counter()
                for _ in range(10):  # Average over multiple runs
                    c_gpu = a_gpu + b_gpu
                cp.cuda.Stream.null.synchronize()
                gpu_time = (time.perf_counter() - start_time) / 10
                
                # Measure CPU performance
                start_time = time.perf_counter()
                c_cpu = a_cpu + b_cpu
                cpu_time = time.perf_counter() - start_time
                
                # Calculate throughput
                elements_per_sec_gpu = size / gpu_time
                elements_per_sec_cpu = size / cpu_time
                speedup = cpu_time / gpu_time
                
                results[size] = {
                    'gpu_time_ms': gpu_time * 1000,
                    'cpu_time_ms': cpu_time * 1000,
                    'gpu_throughput_mops': elements_per_sec_gpu / 1e6,
                    'cpu_throughput_mops': elements_per_sec_cpu / 1e6,
                    'speedup': speedup
                }
                
                logger.info(
                    f"Vector Add {size/1e6:.1f}M: GPU {gpu_time*1000:.2f}ms "
                    f"({elements_per_sec_gpu/1e6:.0f} MOPS), "
                    f"CPU {cpu_time*1000:.2f}ms "
                    f"({elements_per_sec_cpu/1e6:.0f} MOPS), "
                    f"Speedup: {speedup:.1f}x"
                )
                
                # Clean up
                del a_gpu, b_gpu, c_gpu
            
            # Assert reasonable performance for large vectors
            large_size = max(results.keys())
            assert results[large_size]['gpu_throughput_mops'] >= 1000, (
                f"GPU vector throughput too low: "
                f"{results[large_size]['gpu_throughput_mops']:.0f} MOPS"
            )
            
        except ImportError:
            pytest.skip("CuPy not available for vector performance test")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
