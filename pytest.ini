[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
markers =
    cuda: marks tests as requiring CUDA (deselect with '-m "not cuda"')
    slow: marks tests as slow (deselect with '-m "not slow"')
    performance: marks tests as performance benchmarks
    gpu_memory: marks tests that require significant GPU memory
    basic: marks basic functionality tests
    computation: marks computational verification tests
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
