"""
Comprehensive CUDA 12.9 Verification Test Suite

This test suite verifies that CUDA 12.9 is properly installed and functioning
correctly across multiple Python libraries and use cases.
"""

import pytest
import numpy as np
import time
import warnings
from typing import Dict, Any, Tuple
import logging

from cuda_utils import (
    check_cuda_availability,
    get_gpu_properties,
    verify_cuda_version,
    get_memory_info
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)


class TestCUDAAvailability:
    """Test basic CUDA availability and installation."""
    
    @pytest.mark.cuda
    @pytest.mark.basic
    def test_cuda_libraries_available(self):
        """Test that CUDA libraries are available and importable."""
        availability = check_cuda_availability()
        
        # At least one CUDA library should be available
        cuda_available = any([
            availability['cupy'],
            availability['torch'],
            availability['pycuda']
        ])
        
        assert cuda_available, (
            f"No CUDA libraries are available. Errors: {availability['errors']}"
        )
        
        logger.info(f"CUDA availability: {availability}")
    
    @pytest.mark.cuda
    @pytest.mark.basic
    def test_gpu_devices_detected(self):
        """Test that at least one GPU device is detected."""
        gpu_properties = get_gpu_properties()
        
        assert len(gpu_properties) > 0, "No GPU devices detected"
        
        for i, gpu in enumerate(gpu_properties):
            logger.info(f"GPU {i}: {gpu['name']} - {gpu['total_memory_gb']:.2f} GB")
            
            # Basic sanity checks
            assert gpu['total_memory_gb'] > 0, f"GPU {i} has no memory"
            assert gpu['compute_capability'], f"GPU {i} has no compute capability"


class TestCUDAVersion:
    """Test CUDA version verification."""
    
    @pytest.mark.cuda
    @pytest.mark.basic
    def test_cuda_version_12_9(self):
        """Test that CUDA runtime version is 12.9."""
        version_info = verify_cuda_version(expected_major=12, expected_minor=9)
        
        # Check if any library reports the correct version
        any_correct_version = any(version_info['matches'].values())
        
        if not any_correct_version:
            # Provide detailed information about what was found
            actual_versions = version_info['actual']
            expected = version_info['expected']
            
            pytest.fail(
                f"CUDA version mismatch. Expected: {expected}, "
                f"Found: {actual_versions}. "
                f"Errors: {version_info['errors']}"
            )
        
        logger.info(f"CUDA version verification: {version_info}")
    
    @pytest.mark.cuda
    @pytest.mark.basic
    def test_compute_capability_sufficient(self):
        """Test that GPU compute capability is sufficient for CUDA 12.9."""
        gpu_properties = get_gpu_properties()
        
        assert len(gpu_properties) > 0, "No GPU devices found"
        
        for gpu in gpu_properties:
            compute_cap = gpu['compute_capability']
            major, minor = map(int, compute_cap.split('.'))
            
            # CUDA 12.9 requires compute capability 3.5 or higher
            assert major >= 3, (
                f"GPU {gpu['name']} compute capability {compute_cap} "
                f"is too low for CUDA 12.9 (requires 3.5+)"
            )
            
            if major == 3:
                assert minor >= 5, (
                    f"GPU {gpu['name']} compute capability {compute_cap} "
                    f"is too low for CUDA 12.9 (requires 3.5+)"
                )


class TestGPUProperties:
    """Test GPU device properties and capabilities."""
    
    @pytest.mark.cuda
    @pytest.mark.basic
    def test_gpu_memory_sufficient(self):
        """Test that GPU has sufficient memory for basic operations."""
        gpu_properties = get_gpu_properties()
        
        for gpu in gpu_properties:
            # Require at least 1GB of GPU memory
            assert gpu['total_memory_gb'] >= 1.0, (
                f"GPU {gpu['name']} has insufficient memory: "
                f"{gpu['total_memory_gb']:.2f} GB (minimum 1.0 GB required)"
            )
    
    @pytest.mark.cuda
    @pytest.mark.basic
    def test_gpu_multiprocessors(self):
        """Test that GPU has multiprocessors for parallel computation."""
        gpu_properties = get_gpu_properties()
        
        for gpu in gpu_properties:
            mp_count = gpu.get('multiprocessor_count', 0)
            assert mp_count > 0, (
                f"GPU {gpu['name']} has no multiprocessors detected"
            )
    
    @pytest.mark.cuda
    def test_memory_allocation_and_deallocation(self):
        """Test basic GPU memory allocation and deallocation."""
        try:
            import torch
            if not torch.cuda.is_available():
                pytest.skip("PyTorch CUDA not available")
            
            device = torch.device('cuda:0')
            
            # Get initial memory state
            initial_allocated = torch.cuda.memory_allocated(device)
            
            # Allocate a 100MB tensor
            size_mb = 100
            elements = (size_mb * 1024 * 1024) // 4  # 4 bytes per float32
            tensor = torch.randn(elements, device=device, dtype=torch.float32)
            
            # Check memory increased
            after_alloc = torch.cuda.memory_allocated(device)
            assert after_alloc > initial_allocated, "Memory allocation failed"
            
            # Deallocate
            del tensor
            torch.cuda.empty_cache()
            
            # Check memory decreased (allow some tolerance)
            final_allocated = torch.cuda.memory_allocated(device)
            assert final_allocated <= initial_allocated + 1024*1024, (  # 1MB tolerance
                "Memory deallocation failed"
            )
            
        except ImportError:
            pytest.skip("PyTorch not available for memory test")


class TestBasicComputation:
    """Test basic CUDA computations."""
    
    @pytest.mark.cuda
    @pytest.mark.computation
    def test_vector_addition_cupy(self):
        """Test vector addition using CuPy."""
        try:
            import cupy as cp
            
            if not cp.cuda.is_available():
                pytest.skip("CuPy CUDA not available")
            
            # Create test vectors
            size = 1000000
            a_cpu = np.random.randn(size).astype(np.float32)
            b_cpu = np.random.randn(size).astype(np.float32)
            
            # Transfer to GPU
            a_gpu = cp.asarray(a_cpu)
            b_gpu = cp.asarray(b_cpu)
            
            # Perform computation on GPU
            c_gpu = a_gpu + b_gpu
            
            # Transfer result back to CPU
            c_cpu_from_gpu = cp.asnumpy(c_gpu)
            
            # Verify result
            c_cpu_expected = a_cpu + b_cpu
            np.testing.assert_allclose(
                c_cpu_from_gpu, c_cpu_expected, 
                rtol=1e-6, atol=1e-6,
                err_msg="Vector addition results don't match"
            )
            
        except ImportError:
            pytest.skip("CuPy not available for vector addition test")
    
    @pytest.mark.cuda
    @pytest.mark.computation
    def test_matrix_multiplication_torch(self):
        """Test matrix multiplication using PyTorch."""
        try:
            import torch
            
            if not torch.cuda.is_available():
                pytest.skip("PyTorch CUDA not available")
            
            device = torch.device('cuda:0')
            
            # Create test matrices
            size = 1000
            a_cpu = torch.randn(size, size, dtype=torch.float32)
            b_cpu = torch.randn(size, size, dtype=torch.float32)
            
            # Transfer to GPU
            a_gpu = a_cpu.to(device)
            b_gpu = b_cpu.to(device)
            
            # Perform computation on GPU
            c_gpu = torch.matmul(a_gpu, b_gpu)
            
            # Transfer result back to CPU
            c_cpu_from_gpu = c_gpu.cpu()
            
            # Verify result
            c_cpu_expected = torch.matmul(a_cpu, b_cpu)
            torch.testing.assert_close(
                c_cpu_from_gpu, c_cpu_expected,
                rtol=1e-4, atol=1e-4,
                msg="Matrix multiplication results don't match"
            )
            
        except ImportError:
            pytest.skip("PyTorch not available for matrix multiplication test")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
