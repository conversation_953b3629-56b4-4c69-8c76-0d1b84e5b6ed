"""
CUDA Utility Functions for Testing
Provides common CUDA operations and device information utilities.
"""

import sys
import warnings
from typing import Dict, List, Optional, Tuple, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_cuda_availability() -> Dict[str, Any]:
    """
    Check CUDA availability across different libraries.
    
    Returns:
        Dict containing availability status for each library
    """
    availability = {
        'cupy': False,
        'torch': False,
        'pycuda': False,
        'nvidia_ml': False,
        'errors': {}
    }
    import torch

    # Check CuPy
    try:
        import cupy as cp
        availability['cupy'] = cp.cuda.is_available()
        if availability['cupy']:
            availability['cupy_version'] = cp.__version__
            availability['cupy_cuda_version'] = cp.cuda.runtime.runtimeGetVersion()
    except Exception as e:
        availability['errors']['cupy'] = str(e)
    
    # Check PyTorch
    try:
        import torch
        availability['torch'] = torch.cuda.is_available()
        if availability['torch']:
            availability['torch_version'] = torch.__version__
            availability['torch_cuda_version'] = torch.version.cuda
            availability['torch_device_count'] = torch.cuda.device_count()
    except Exception as e:
        availability['errors']['torch'] = str(e)
    
    # Check PyCUDA
    try:
        import pycuda.driver as cuda
        import pycuda.autoinit
        availability['pycuda'] = True
        availability['pycuda_version'] = cuda.get_version()
    except Exception as e:
        availability['errors']['pycuda'] = str(e)
    
    # Check NVIDIA ML
    try:
        import pynvml
        pynvml.nvmlInit()
        availability['nvidia_ml'] = True
        availability['nvidia_ml_driver'] = pynvml.nvmlSystemGetDriverVersion()
    except Exception as e:
        availability['errors']['nvidia_ml'] = str(e)
    
    return availability

def get_gpu_properties() -> List[Dict[str, Any]]:
    """
    Get detailed properties of all available GPUs.
    
    Returns:
        List of dictionaries containing GPU properties
    """
    gpu_properties = []
    
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                gpu_info = {
                    'device_id': i,
                    'name': props.name,
                    'compute_capability': f"{props.major}.{props.minor}",
                    'total_memory_gb': props.total_memory / (1024**3),
                    'multiprocessor_count': props.multi_processor_count,
                    'max_threads_per_multiprocessor': props.max_threads_per_multi_processor,
                    'max_threads_per_block': props.max_threads_per_block,
                    'warp_size': props.warp_size
                }
                gpu_properties.append(gpu_info)
    except Exception as e:
        logger.error(f"Error getting GPU properties with PyTorch: {e}")
    
    # Try with CuPy if PyTorch failed
    if not gpu_properties:
        try:
            import cupy as cp
            if cp.cuda.is_available():
                device = cp.cuda.Device()
                gpu_info = {
                    'device_id': device.id,
                    'name': device.attributes.get('Name', 'Unknown'),
                    'compute_capability': f"{device.compute_capability[0]}.{device.compute_capability[1]}",
                    'total_memory_gb': device.mem_info[1] / (1024**3),
                    'multiprocessor_count': device.attributes.get('MultiprocessorCount', 'Unknown')
                }
                gpu_properties.append(gpu_info)
        except Exception as e:
            logger.error(f"Error getting GPU properties with CuPy: {e}")
    
    return gpu_properties

def verify_cuda_version(expected_major: int = 12, expected_minor: int = 9) -> Dict[str, Any]:
    """
    Verify CUDA runtime version matches expected version.
    
    Args:
        expected_major: Expected CUDA major version
        expected_minor: Expected CUDA minor version
        
    Returns:
        Dictionary with version verification results
    """
    version_info = {
        'expected': f"{expected_major}.{expected_minor}",
        'actual': {},
        'matches': {},
        'errors': {}
    }
    
    # Check CuPy CUDA version
    try:
        import cupy as cp
        if cp.cuda.is_available():
            runtime_version = cp.cuda.runtime.runtimeGetVersion()
            major = runtime_version // 1000
            minor = (runtime_version % 1000) // 10
            version_info['actual']['cupy'] = f"{major}.{minor}"
            version_info['matches']['cupy'] = (major == expected_major and minor == expected_minor)
    except Exception as e:
        version_info['errors']['cupy'] = str(e)
    
    # Check PyTorch CUDA version
    try:
        import torch
        if torch.cuda.is_available():
            cuda_version = torch.version.cuda
            version_info['actual']['torch'] = cuda_version
            if cuda_version:
                major, minor = map(int, cuda_version.split('.')[:2])
                version_info['matches']['torch'] = (major == expected_major and minor == expected_minor)
    except Exception as e:
        version_info['errors']['torch'] = str(e)
    
    return version_info

def get_memory_info() -> Dict[str, Any]:
    """
    Get GPU memory information.
    
    Returns:
        Dictionary with memory information for all GPUs
    """
    memory_info = {}
    
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                torch.cuda.set_device(i)
                total = torch.cuda.get_device_properties(i).total_memory
                allocated = torch.cuda.memory_allocated(i)
                cached = torch.cuda.memory_reserved(i)
                
                memory_info[f'gpu_{i}'] = {
                    'total_gb': total / (1024**3),
                    'allocated_gb': allocated / (1024**3),
                    'cached_gb': cached / (1024**3),
                    'free_gb': (total - allocated) / (1024**3),
                    'utilization_percent': (allocated / total) * 100
                }
    except Exception as e:
        logger.error(f"Error getting memory info: {e}")
    
    return memory_info
