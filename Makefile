# CUDA Test Suite Makefile

.PHONY: help install test test-basic test-quick test-all test-performance info clean

help:
	@echo "CUDA 12.9 Verification Test Suite"
	@echo ""
	@echo "Available targets:"
	@echo "  install      - Install required dependencies"
	@echo "  test-basic   - Run basic CUDA verification tests"
	@echo "  test-quick   - Run quick tests (no slow benchmarks)"
	@echo "  test-all     - Run complete test suite"
	@echo "  test-perf    - Run performance benchmarks only"
	@echo "  info         - Display CUDA system information"
	@echo "  clean        - Clean up temporary files"
	@echo ""
	@echo "Examples:"
	@echo "  make install"
	@echo "  make test-basic"
	@echo "  make info"

install:
	@echo "Installing CUDA test dependencies..."
	pip install -r requirements.txt

test-basic:
	@echo "Running basic CUDA tests..."
	python run_cuda_tests.py basic

test-quick:
	@echo "Running quick CUDA tests..."
	python run_cuda_tests.py quick

test-all:
	@echo "Running complete CUDA test suite..."
	python run_cuda_tests.py all

test-perf:
	@echo "Running CUDA performance benchmarks..."
	python run_cuda_tests.py performance

info:
	@echo "Displaying CUDA system information..."
	python run_cuda_tests.py info

clean:
	@echo "Cleaning up temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete

# Direct pytest commands
pytest-basic:
	pytest test_cuda_verification.py -m basic -v

pytest-computation:
	pytest test_cuda_verification.py -m computation -v

pytest-performance:
	pytest test_cuda_performance.py -m performance -v

pytest-all:
	pytest test_cuda_verification.py test_cuda_performance.py -v
